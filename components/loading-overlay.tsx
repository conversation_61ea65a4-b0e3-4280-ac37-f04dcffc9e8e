"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface LoadingOverlayProps {
  isLoading: boolean;
  onLoadingComplete?: () => void;
}

export function LoadingOverlay({ isLoading, onLoadingComplete }: LoadingOverlayProps) {
  const [showOverlay, setShowOverlay] = useState(isLoading);

  useEffect(() => {
    if (isLoading) {
      setShowOverlay(true);
    } else {
      // Delay hiding to allow exit animation
      const timer = setTimeout(() => {
        setShowOverlay(false);
        onLoadingComplete?.();
      }, 2500);
      return () => clearTimeout(timer);
    }
  }, [isLoading, onLoadingComplete]);

  return (
    <AnimatePresence>
      {showOverlay && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: isLoading ? 1 : 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: isLoading ? 0 : 2 }}
          className="fixed inset-0 z-[9999] pointer-events-none"
        >
          {/* SVG Curtain Animation */}
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 2215 1407"
            preserveAspectRatio="none"
            className="absolute inset-0 w-full h-full"
            style={{ width: '100vw', height: '100vh' }}
          >
            <defs>
              {/* Animated gradient for teardrop */}
              <linearGradient id="tearGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" className="tear-gradient-start" stopColor="#000000">
                  <animate
                    attributeName="stop-color"
                    values="#000000;#f9bc60;#ffd700;#f9bc60"
                    dur="2.5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" className="tear-gradient-end" stopColor="#000000">
                  <animate
                    attributeName="stop-color"
                    values="#000000;#e6a050;#ffcc00;#e6a050"
                    dur="2.5s"
                    repeatCount="indefinite"
                  />
                </stop>
              </linearGradient>

              {/* Glow filter for teardrop */}
              <filter id="tearGlow">
                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>

              {/* Define clipping paths for left and right halves */}
              <clipPath id="leftHalf">
                <rect x="0" y="0" width="1107.5" height="1407" />
              </clipPath>
              <clipPath id="rightHalf">
                <rect x="1107.5" y="0" width="1107.5" height="1407" />
              </clipPath>
            </defs>

            {/* Left curtain */}
            <g className={`curtain-left ${!isLoading ? 'animate-slide-left' : ''}`}>
              <rect 
                x="0" 
                y="0" 
                width="1107.5" 
                height="1407" 
                fill="black"
                className="curtain-piece"
              />
            </g>

            {/* Right curtain */}
            <g className={`curtain-right ${!isLoading ? 'animate-slide-right' : ''}`}>
              <rect 
                x="1107.5" 
                y="0" 
                width="1107.5" 
                height="1407" 
                fill="black"
                className="curtain-piece"
              />
            </g>

            {/* Animated teardrop in center */}
            <g className={`teardrop-container ${!isLoading ? 'animate-tear-fade' : ''}`}>
              <path
                d="M1107.5 658C1103.72 695.615 1080 695.443 1080 721.378C1080 734.603 1091.52 748 1107.5 748C1123.48 748 1135 734.088 1135 721.378C1135 695.443 1110.59 695.443 1107.5 658Z"
                fill="url(#tearGradient)"
                filter="url(#tearGlow)"
                className="teardrop animate-pulse-glow"
              />
            </g>
          </svg>

          {/* Optional loading text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-center"
            >
              <h2 className="text-2xl md:text-4xl font-bold text-yellow-400 drop-shadow-lg animate-shimmer">
                Tears of the Left
              </h2>
              <p className="text-sm md:text-base text-yellow-300/80 mt-2 animate-fade-in">
                Loading emotional transformation...
              </p>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}