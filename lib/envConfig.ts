/**
 * Environment Configuration
 * 
 * This file centralizes environment variable access and provides type safety.
 * It ensures sensitive server-side variables are never exposed to the client.
 */

// Client-side environment variables (safe to expose)
export const clientEnv = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,
  },
  site: {
    url: process.env.NEXT_PUBLIC_SITE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'),
  },
  upload: {
    maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),
    allowedTypes: process.env.NEXT_PUBLIC_ALLOWED_TYPES?.split(',') || ['image/jpeg', 'image/png', 'image/webp'],
  },
} as const;

// Server-side environment variables (NEVER expose to client)
export const serverEnv = {
  supabase: {
    serviceKey: process.env.SUPABASE_SERVICE_KEY!,
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY!,
  },
  openrouter: {
    apiKey: process.env.OPENROUTER_API_KEY!,
  },
  model: process.env.MODEL || 'gpt-3.5-turbo',
  imageModel: process.env.IMAGE_MODEL || 'gpt-image-1',
  nodeEnv: process.env.NODE_ENV || 'development',
} as const;

// Validation functions
export function validateClientEnv() {
  const missing: string[] = [];
  
  if (!clientEnv.supabase.url) missing.push('NEXT_PUBLIC_SUPABASE_URL');
  if (!clientEnv.supabase.anonKey) missing.push('NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY');
  
  if (missing.length > 0) {
    throw new Error(`Missing required client environment variables: ${missing.join(', ')}`);
  }
}

export function validateServerEnv() {
  const missing: string[] = [];
  
  if (!serverEnv.supabase.serviceKey) missing.push('SUPABASE_SERVICE_KEY');
  if (!serverEnv.openai.apiKey) missing.push('OPENAI_API_KEY');
  if (!serverEnv.openrouter.apiKey) missing.push('OPENROUTER_API_KEY');
  
  if (missing.length > 0) {
    throw new Error(`Missing required server environment variables: ${missing.join(', ')}`);
  }
}

// Runtime check to prevent server variables from being accessed on client
if (typeof window !== 'undefined') {
  // We're on the client - create a proxy that throws if server env is accessed
  const serverEnvProxy = new Proxy({}, {
    get() {
      throw new Error('Attempted to access server environment variables on client side');
    }
  });
  
  // Override serverEnv on client to prevent accidental access
  Object.defineProperty(exports, 'serverEnv', {
    get: () => serverEnvProxy,
    configurable: false,
    enumerable: true
  });
}