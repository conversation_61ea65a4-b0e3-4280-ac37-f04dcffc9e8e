import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';
import { enforceRateLimitAtomic, logGenerationAttempt } from '@/lib/rateLimiter';
import { sanitizeTextInput } from '@/lib/inputSanitization';
import { handleApiError } from '@/lib/errorSanitization';
import { serverEnv, clientEnv } from '@/lib/envConfig';

// Hardcoded prompt for text processing
const HARDCODED_PROMPT = "Transform this text into a more emotional and expressive version with deeper feeling and poetic language. Keep the core message but make it more touching and heartfelt:";

export async function POST(request: NextRequest) {
  let userId: number = 0;
  let rateLimitStatus: { 
    userRemaining: number;
    globalRemaining: number;
    userLimit: number;
    globalLimit: number;
  } | null = null;

  try {
    // Check required environment variables using centralized config
    const openrouterApiKey = serverEnv.openrouter.apiKey;
    const model = serverEnv.model;

    if (!openrouterApiKey) {
      return NextResponse.json({ 
        error: 'OpenRouter API key not configured' 
      }, { status: 500 });
    }

    // Initialize OpenAI client for OpenRouter with security configurations
    const openai = new OpenAI({
      apiKey: openrouterApiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      timeout: 30000, // 30 second timeout for text processing
      maxRetries: 3,
      defaultHeaders: {
        'HTTP-Referer': clientEnv.site.url,
        'X-Title': 'Tears of the Left - Text Processing',
        'User-Agent': 'CryBaby/1.0'
      }
    });

    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔐 Authenticated user:', user.id);

    // Check rate limits atomically before processing
    console.log('🛡️ Checking rate limits atomically...');
    const rateLimitResult = await enforceRateLimitAtomic(user.id);
    userId = rateLimitResult.userId;
    rateLimitStatus = rateLimitResult.status;

    if (!rateLimitResult.allowed) {
      console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);

      // Log the blocked attempt
      await logGenerationAttempt({
        userId,
        prompt: 'Rate limit exceeded',
        success: false,
        errorMessage: rateLimitResult.status.reason
      });

      return NextResponse.json({
        error: rateLimitResult.status.reason,
        rateLimitStatus: rateLimitResult.status
      }, { status: 429 });
    }

    console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);

    const { text } = await request.json();

    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return NextResponse.json({ 
        error: 'Text input is required' 
      }, { status: 400 });
    }

    // Sanitize and validate text input
    const sanitizationResult = sanitizeTextInput(text);
    const sanitizedText = sanitizationResult.sanitized;

    // Log warnings if any
    if (sanitizationResult.warnings.length > 0) {
      console.warn('🚨 Text sanitization warnings:', sanitizationResult.warnings);
    }

    // Check if input is valid after sanitization
    if (!sanitizationResult.isValid) {
      console.error('❌ Text validation failed:', sanitizationResult.warnings);
      return NextResponse.json({
        error: 'Invalid input detected. Please check your text and try again.',
        details: sanitizationResult.warnings
      }, { status: 400 });
    }

    // Validate text length after sanitization
    if (sanitizedText.length === 0) {
      return NextResponse.json({ 
        error: 'Text cannot be empty after security processing.' 
      }, { status: 400 });
    }

    console.log('Processing text with OpenRouter:', {
      model,
      originalLength: text.length,
      sanitizedLength: sanitizedText.length,
      prompt: HARDCODED_PROMPT
    });

    // Process text with OpenRouter using sanitized input
    const response = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: HARDCODED_PROMPT
        },
        {
          role: 'user',
          content: sanitizedText
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    });

    console.log('Model used:', model);
    console.log('Response received from OpenRouter');

    const processedText = response.choices[0]?.message?.content;

    if (!processedText) {
      console.error('No processed text found in OpenRouter response');

      // Log the failed generation attempt
      await logGenerationAttempt({
        userId,
        prompt: sanitizedText,
        success: false,
        errorMessage: `No processed text found. Model: ${model}`
      });

      throw new Error(`No processed text found. Model: ${model}`);
    }

    // Rate limits were already decremented atomically during the check
    console.log('✅ Text processing successful, rate limits already decremented atomically');

    // Log the successful generation attempt
    await logGenerationAttempt({
      userId,
      prompt: sanitizedText,
      success: true,
      imageUrl: undefined // No image for text processing
    });

    console.log('🎉 Text processing completed successfully');

    return NextResponse.json({
      success: true,
      processedText,
      originalText: sanitizedText,
      processedAt: new Date().toISOString(),
      model: model,
      rateLimitStatus: {
        userRemaining: rateLimitStatus.userRemaining,
        globalRemaining: rateLimitStatus.globalRemaining,
        userLimit: rateLimitStatus.userLimit,
        globalLimit: rateLimitStatus.globalLimit
      }
    });

  } catch (error) {
    // Log the failed generation attempt if we have user info
    if (userId > 0) {
      await logGenerationAttempt({
        userId,
        prompt: 'Error occurred during text processing',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Use centralized error handling
    const { response, statusCode } = handleApiError(
      error,
      'Text Processing',
      userId > 0 ? userId.toString() : undefined
    );

    return NextResponse.json(response, { status: statusCode });
  }
}